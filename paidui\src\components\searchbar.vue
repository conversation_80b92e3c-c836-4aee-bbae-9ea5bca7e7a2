<template>
  <div>
    <div class="search-con" :class="searchConClass">
      <div v-if="!isBig" class="toggle-city" @click="openChangeProv">
        <span>{{ geo.cityName }}</span>
        <span class="icon-arrow-icon"></span>
      </div>
      <div class="search-bar" @click="gotoNearby">
        <input
          v-model="searchCriteria"
          :placeholder="placeholder || $t('langPlaceholder')"
          placeholder-style="color:rgba(0,0,0,0.6)"
          :disabled="isBig"
          @focus="handleInputFocus"
          @blur="handleInputBlur"
        />
        <span class="iconfont icon-search-icon"></span>
      </div>
    </div>
    <ProvinceCon ref="provinceCon" @toggleCity="changeCity" />
  </div>
</template>
<script>
import {debounce} from '@/utils/utils.js'
import ProvinceCon from '@/components/province.vue'
import { useUserStore } from '@/store/user'
import { mapState } from 'pinia'
import { gdpDcsMultiTrack } from "@/utils/gio-track.js"

export default {
  name: 'SearchBar',

  data() {
    return {
      searchCriteria: ''
    }
  },

  props: {
    isBig: {
      type: Boolean,
      default: false
    },
    placeholder: {
      type: String,
      default: ''
    },
    bigFont: {
      type: Boolean,
      default: false
    }
  },
  components: {
    ProvinceCon
  },

  computed: {
    ...mapState(useUserStore, ['geo']),

    searchConClass() {
      return {
        'big': this.isBig,
        'small': !this.isBig,
        'care-con': this.bigFont
      }
    }
  },
  watch: {
    searchCriteria: {
      handler(val) {
        if (val && val.trim()) {
          this.onChange()
        }
      },
      immediate: false
    }
  },

  created() {
    this.gdpTrack('imp')
  },
  methods: {
    onChange: debounce(function() {
      this.getNearShop()
    }, 1000),

    /**
     * 查询附近店铺数据
     */
    getNearShop() {
      if (this.searchCriteria && this.searchCriteria.trim()) {
        this.$emit('getNearShop', this.searchCriteria.trim())
      }
    },

    /**
     * 输入框获得焦点
     */
    handleInputFocus() {
      this.gdpTrack('clk')
    },

    /**
     * 输入框失去焦点
     */
    handleInputBlur() {
      // 可以在这里添加失焦时的逻辑
    },

    /**
     * 跳转附近店铺页面
     */
    gotoNearby() {
      if (this.isBig) {
        this.gdpTrack('clk')
        const url = '/pages/shoplist/index'
        uni.navigateTo({ url })
      }
    },

    /**
     * 打开省份选择器
     */
    openChangeProv() {
      this.$refs.provinceCon.open()
    },

    /**
     * 切换城市
     * @param {Object} val 城市信息
     */
    changeCity(val) {
      const user = useUserStore()
      user.setGeo(val)
      this.getNearShop()
    },

    /**
     * GDP 埋点追踪
     * @param {string} type 追踪类型
     */
    gdpTrack(type) {
      const name = '搜索框'
      gdpDcsMultiTrack(type, {
        WT_et: type,
        WT_area_type_1: '楼层',
        WT_area_name: name,
        WT_envName: name,
        XY_env_type: 'button'
      })
    }
  }
}
</script>
<style lang="scss" scoped>
@import "@/assets/css/search/search.scss";

.icon-arrow-icon {
  width: 17px;
  height: 17px;
  background: url("@/static/home/<USER>") center no-repeat;
  background-size: contain;
  margin: 0 20px 0 8px;
  transform: rotate(90deg);
}

.toggle-city {
  display: flex;
  align-items: center;
}

.care-con {
  .toggle-city {
    font-size: 36px;
  }
}
</style>
