"use strict";
const common_vendor = require("../common/vendor.js");
const utils_utils = require("../utils/utils.js");
const store_user = require("../store/user.js");
const utils_gioTrack = require("../utils/gio-track.js");
const ProvinceCon = () => "./province.js";
const _sfc_main = {
  name: "SearchBar",
  data() {
    return {
      searchCriteria: ""
    };
  },
  props: {
    isBig: {
      type: Boolean,
      default: false
    },
    placeholder: {
      type: String,
      default: ""
    },
    bigFont: {
      type: <PERSON>olean,
      default: false
    }
  },
  components: {
    ProvinceCon
  },
  computed: {
    ...common_vendor.mapState(store_user.useUserStore, ["geo"]),
    searchConClass() {
      return {
        "big": this.isBig,
        "small": !this.isBig,
        "care-con": this.bigFont
      };
    }
  },
  watch: {
    searchCriteria: {
      handler(val) {
        if (val && val.trim()) {
          this.onChange();
        }
      },
      immediate: false
    }
  },
  created() {
    this.gdpTrack("imp");
  },
  methods: {
    onChange: utils_utils.debounce(function() {
      this.getNearShop();
    }, 1e3),
    /**
     * 查询附近店铺数据
     */
    getNearShop() {
      if (this.searchCriteria && this.searchCriteria.trim()) {
        this.$emit("getNearShop", this.searchCriteria.trim());
      }
    },
    /**
     * 输入框获得焦点
     */
    handleInputFocus() {
      this.gdpTrack("clk");
    },
    /**
     * 输入框失去焦点
     */
    handleInputBlur() {
    },
    /**
     * 跳转附近店铺页面
     */
    gotoNearby() {
      if (this.isBig) {
        this.gdpTrack("clk");
        const url = "/pages/shoplist/index";
        common_vendor.index.navigateTo({ url });
      }
    },
    /**
     * 打开省份选择器
     */
    openChangeProv() {
      this.$refs.provinceCon.open();
    },
    /**
     * 切换城市
     * @param {Object} val 城市信息
     */
    changeCity(val) {
      const user = store_user.useUserStore();
      user.setGeo(val);
      this.getNearShop();
    },
    /**
     * GDP 埋点追踪
     * @param {string} type 追踪类型
     */
    gdpTrack(type) {
      const name = "搜索框";
      utils_gioTrack.gdpDcsMultiTrack(type, {
        WT_et: type,
        WT_area_type_1: "楼层",
        WT_area_name: name,
        WT_envName: name,
        XY_env_type: "button"
      });
    }
  }
};
if (!Array) {
  const _component_ProvinceCon = common_vendor.resolveComponent("ProvinceCon");
  _component_ProvinceCon();
}
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: !$props.isBig
  }, !$props.isBig ? {
    b: common_vendor.t(_ctx.geo.cityName),
    c: common_vendor.o((...args) => $options.openChangeProv && $options.openChangeProv(...args))
  } : {}, {
    d: $props.placeholder || _ctx.$t("langPlaceholder"),
    e: $props.isBig,
    f: common_vendor.o((...args) => $options.handleInputFocus && $options.handleInputFocus(...args)),
    g: common_vendor.o((...args) => $options.handleInputBlur && $options.handleInputBlur(...args)),
    h: $data.searchCriteria,
    i: common_vendor.o(($event) => $data.searchCriteria = $event.detail.value),
    j: common_vendor.o((...args) => $options.gotoNearby && $options.gotoNearby(...args)),
    k: common_vendor.n($options.searchConClass),
    l: common_vendor.o($options.changeCity)
  });
}
const Component = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-d926064c"]]);
my.createComponent(Component);
